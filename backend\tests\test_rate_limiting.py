"""
Security tests for rate limiting and brute force protection
Tests login attempt limits, API rate limiting, and DoS protection
"""

import pytest
import time
from models import User, db


class TestLoginRateLimiting:
    """Test rate limiting for login attempts"""
    
    def test_login_brute_force_protection(self, client, test_user):
        """Test protection against brute force login attacks"""
        # Attempt multiple failed logins
        failed_attempts = 0
        max_attempts = 10  # Try up to 10 failed attempts
        
        for attempt in range(max_attempts):
            login_data = {
                'employee_number': test_user.employee_number,
                'password': 'wrongpassword'
            }
            
            response = client.post('/api/auth/login', json=login_data)
            
            if response.status_code == 429:  # Rate limited
                # Rate limiting is working
                assert attempt >= 3, "Rate limiting should kick in after a few attempts"
                break
            elif response.status_code == 401:  # Unauthorized (normal failed login)
                failed_attempts += 1
                continue
            else:
                # Unexpected response
                assert False, f"Unexpected response code: {response.status_code}"
        
        # If we made it through all attempts without rate limiting,
        # that might be acceptable but should be documented
        if failed_attempts == max_attempts:
            print("Warning: No rate limiting detected for login attempts")
    
    def test_login_rate_limit_reset(self, client, test_user):
        """Test that rate limits reset after time period"""
        # Make several failed attempts to trigger rate limiting
        for _ in range(5):
            login_data = {
                'employee_number': test_user.employee_number,
                'password': 'wrongpassword'
            }
            response = client.post('/api/auth/login', json=login_data)
            
            if response.status_code == 429:
                # Rate limited - now test if it resets
                break
        
        # Wait a short time (rate limits should reset quickly in tests)
        time.sleep(2)
        
        # Try a valid login
        login_data = {
            'employee_number': test_user.employee_number,
            'password': 'testpass123'
        }
        response = client.post('/api/auth/login', json=login_data)
        
        # Should be able to login with correct credentials
        # (unless rate limit window is longer than our wait time)
        assert response.status_code in [200, 429]
    
    def test_account_lockout_protection(self, client, test_user):
        """Test account lockout after multiple failed attempts"""
        # Make many failed login attempts
        for attempt in range(15):
            login_data = {
                'employee_number': test_user.employee_number,
                'password': 'wrongpassword'
            }
            response = client.post('/api/auth/login', json=login_data)
            
            # Check if account gets locked
            if response.status_code == 423:  # Locked
                assert attempt >= 5, "Account should lock after several failed attempts"
                break
            elif response.status_code in [401, 429]:
                continue
            else:
                assert False, f"Unexpected response code: {response.status_code}"


class TestAPIRateLimiting:
    """Test rate limiting for API endpoints"""
    
    def test_api_endpoint_rate_limiting(self, client, auth_headers):
        """Test rate limiting on API endpoints"""
        # Test rate limiting on a common endpoint
        rate_limited = False
        
        for request_num in range(100):  # Make many requests quickly
            response = client.get('/api/tools', headers=auth_headers)
            
            if response.status_code == 429:  # Rate limited
                rate_limited = True
                assert request_num >= 10, "Rate limiting should allow reasonable number of requests"
                break
            elif response.status_code == 200:
                continue
            else:
                # Other error codes are acceptable
                break
        
        # Rate limiting might not be implemented, which is acceptable for internal tools
        if not rate_limited:
            print("Warning: No API rate limiting detected")
    
    def test_different_endpoints_separate_limits(self, client, auth_headers):
        """Test that different endpoints have separate rate limits"""
        endpoints = [
            '/api/tools',
            '/api/chemicals',
            '/api/checkouts',
            '/api/user/profile'
        ]
        
        # Make requests to different endpoints
        for endpoint in endpoints:
            for _ in range(20):  # Make multiple requests to each
                response = client.get(endpoint, headers=auth_headers)
                
                if response.status_code == 429:
                    # If one endpoint is rate limited, others should still work
                    for other_endpoint in endpoints:
                        if other_endpoint != endpoint:
                            other_response = client.get(other_endpoint, headers=auth_headers)
                            # Other endpoints should not be immediately rate limited
                            assert other_response.status_code != 429, \
                                f"Rate limiting on {endpoint} should not affect {other_endpoint}"
                    break
    
    def test_rate_limit_headers(self, client, auth_headers):
        """Test that rate limit headers are present"""
        response = client.get('/api/tools', headers=auth_headers)
        
        # Check for common rate limiting headers
        rate_limit_headers = [
            'X-RateLimit-Limit',
            'X-RateLimit-Remaining',
            'X-RateLimit-Reset',
            'Retry-After'
        ]
        
        has_rate_limit_headers = any(header in response.headers for header in rate_limit_headers)
        
        if not has_rate_limit_headers:
            print("Warning: No rate limiting headers found")


class TestDDoSProtection:
    """Test protection against Distributed Denial of Service attacks"""
    
    def test_large_request_handling(self, client, auth_headers):
        """Test handling of unusually large requests"""
        # Create a very large payload
        large_data = {
            'tool_number': 'LARGE001',
            'description': 'A' * 100000,  # Very long description
            'condition': 'Good',
            'location': 'Test Lab',
            'category': 'Testing'
        }
        
        response = client.post('/api/tools', json=large_data, headers=auth_headers)
        
        # Should reject or handle large requests gracefully
        assert response.status_code in [400, 413, 422], "Should reject overly large requests"
    
    def test_concurrent_request_handling(self, client, auth_headers):
        """Test handling of many concurrent requests"""
        import threading
        import queue
        
        results = queue.Queue()
        
        def make_request():
            try:
                response = client.get('/api/tools', headers=auth_headers)
                results.put(response.status_code)
            except Exception as e:
                results.put(f"Error: {e}")
        
        # Create multiple threads to simulate concurrent requests
        threads = []
        for _ in range(20):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Collect results
        status_codes = []
        while not results.empty():
            status_codes.append(results.get())
        
        # Most requests should succeed or be rate limited
        successful_requests = sum(1 for code in status_codes if code == 200)
        rate_limited_requests = sum(1 for code in status_codes if code == 429)
        error_requests = sum(1 for code in status_codes if isinstance(code, str))
        
        # Should handle concurrent requests without crashing
        assert error_requests < len(threads) / 2, "Too many requests resulted in errors"
        assert successful_requests > 0, "At least some requests should succeed"
    
    def test_malformed_request_handling(self, client, auth_headers):
        """Test handling of malformed requests"""
        malformed_requests = [
            # Invalid JSON
            ('POST', '/api/tools', 'invalid json{'),
            # Missing content type
            ('POST', '/api/tools', '{"valid": "json"}'),
            # Empty body
            ('POST', '/api/tools', ''),
            # Null bytes
            ('POST', '/api/tools', '\x00\x01\x02'),
        ]
        
        for method, endpoint, data in malformed_requests:
            if method == 'POST':
                response = client.post(
                    endpoint,
                    data=data,
                    headers=auth_headers,
                    content_type='application/json'
                )
            
            # Should handle malformed requests gracefully
            assert response.status_code in [400, 422], \
                f"Should reject malformed request: {method} {endpoint}"
            
            # Should not cause server errors
            assert response.status_code != 500, \
                f"Malformed request should not cause server error: {method} {endpoint}"


class TestResourceExhaustion:
    """Test protection against resource exhaustion attacks"""
    
    def test_memory_exhaustion_protection(self, client, auth_headers):
        """Test protection against memory exhaustion"""
        # Try to create many objects quickly
        created_objects = 0
        
        for i in range(100):
            tool_data = {
                'tool_number': f'MEM{i:03d}',
                'description': f'Memory test tool {i}',
                'condition': 'Good',
                'location': 'Test Lab',
                'category': 'Testing'
            }
            
            response = client.post('/api/tools', json=tool_data, headers=auth_headers)
            
            if response.status_code in [200, 201]:
                created_objects += 1
            elif response.status_code == 429:  # Rate limited
                break
            elif response.status_code in [400, 422]:  # Validation error
                break
            else:
                # Other errors
                break
        
        # Should either create objects successfully or rate limit
        assert created_objects > 0 or response.status_code == 429, \
            "Should either create objects or implement rate limiting"
    
    def test_database_connection_exhaustion(self, client, auth_headers):
        """Test handling when database connections are exhausted"""
        # This is difficult to test without actually exhausting connections
        # Instead, test that the application handles database errors gracefully
        
        # Make many database-intensive requests
        for _ in range(50):
            response = client.get('/api/tools?search=test', headers=auth_headers)
            
            # Should not cause server errors even under load
            assert response.status_code != 500, \
                "Database-intensive requests should not cause server errors"
            
            if response.status_code == 429:  # Rate limited
                break
