{"name": "supplyline-mro-suite", "private": true, "version": "4.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@reduxjs/toolkit": "^2.7.0", "axios": "^1.9.0", "bootstrap": "^5.3.5", "bootstrap-icons": "^1.12.1", "chart.js": "^4.4.9", "file-saver": "^2.0.5", "html5-qrcode": "^2.3.8", "jsbarcode": "^3.11.6", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "prop-types": "^15.8.1", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-bootstrap": "^2.10.9", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.3", "react-spinners": "^0.17.0", "recharts": "^2.15.3", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.1"}}