/**
 * ToastNotification Component Styles
 */

/* Toast notification container */
.toast-notification-container {
  z-index: 1050;
  position: fixed;
}

/* Base toast styles */
.toast-notification {
  min-width: 300px;
  max-width: 400px;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  border: none;
  animation: slideIn 0.3s ease-out;
}

/* Toast types */
.toast-success {
  background-color: #d1edff;
  border-left: 4px solid #198754;
}

.toast-error {
  background-color: #f8d7da;
  border-left: 4px solid #dc3545;
}

.toast-warning {
  background-color: #fff3cd;
  border-left: 4px solid #ffc107;
}

.toast-info {
  background-color: #d1ecf1;
  border-left: 4px solid #0dcaf0;
}

/* Toast header styles */
.toast-notification .toast-header {
  background-color: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.75rem 1rem;
}

.toast-success .toast-header {
  color: #0f5132;
  border-bottom-color: rgba(25, 135, 84, 0.2);
}

.toast-error .toast-header {
  color: #721c24;
  border-bottom-color: rgba(220, 53, 69, 0.2);
}

.toast-warning .toast-header {
  color: #664d03;
  border-bottom-color: rgba(255, 193, 7, 0.2);
}

.toast-info .toast-header {
  color: #055160;
  border-bottom-color: rgba(13, 202, 240, 0.2);
}

/* Toast body styles */
.toast-notification .toast-body {
  padding: 0.75rem 1rem;
  color: inherit;
}

.toast-success .toast-body {
  color: #0f5132;
  background-color: rgba(25, 135, 84, 0.05);
}

.toast-error .toast-body {
  color: #721c24;
  background-color: rgba(220, 53, 69, 0.05);
}

.toast-warning .toast-body {
  color: #664d03;
  background-color: rgba(255, 193, 7, 0.05);
}

.toast-info .toast-body {
  color: #055160;
  background-color: rgba(13, 202, 240, 0.05);
}

/* Toast icon styles */
.toast-icon {
  font-size: 1.1rem;
  flex-shrink: 0;
}

/* Toast title styles */
.toast-title {
  font-weight: 600;
  font-size: 0.95rem;
}

/* Toast message styles */
.toast-message {
  font-size: 0.9rem;
  line-height: 1.4;
  flex: 1;
}

/* Close button styles */
.toast-notification .btn-close {
  font-size: 0.8rem;
  opacity: 0.6;
}

.toast-notification .btn-close:hover {
  opacity: 1;
}

/* Animation styles */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

.toast-notification.hiding {
  animation: slideOut 0.3s ease-in;
}

/* Hover effects */
.toast-notification:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* Click effects for interactive toasts */
.toast-notification[role="button"] {
  cursor: pointer;
}

.toast-notification[role="button"]:active {
  transform: translateY(0);
}

/* Responsive design */
@media (max-width: 576px) {
  .toast-notification {
    min-width: 280px;
    max-width: 90vw;
    margin-left: 1rem;
    margin-right: 1rem;
  }
  
  .toast-notification .toast-header,
  .toast-notification .toast-body {
    padding: 0.5rem 0.75rem;
  }
  
  .toast-title {
    font-size: 0.9rem;
  }
  
  .toast-message {
    font-size: 0.85rem;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .toast-notification {
    border: 2px solid;
  }
  
  .toast-success {
    border-color: #198754;
  }
  
  .toast-error {
    border-color: #dc3545;
  }
  
  .toast-warning {
    border-color: #ffc107;
  }
  
  .toast-info {
    border-color: #0dcaf0;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .toast-success {
    background-color: #0f2419;
    color: #75b798;
  }
  
  .toast-error {
    background-color: #2c0b0e;
    color: #ea868f;
  }
  
  .toast-warning {
    background-color: #332701;
    color: #ffda6a;
  }
  
  .toast-info {
    background-color: #032830;
    color: #6edff6;
  }
  
  .toast-notification .toast-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .toast-notification .btn-close {
    filter: invert(1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .toast-notification {
    animation: none;
  }
  
  .toast-notification.hiding {
    animation: none;
  }
  
  .toast-notification:hover {
    transform: none;
    transition: none;
  }
}

/* Focus styles for accessibility */
.toast-notification:focus-within {
  outline: 2px solid #0d6efd;
  outline-offset: 2px;
}

/* Position-specific adjustments */
.toast-notification-container[class*="top"] .toast-notification {
  animation: slideInFromTop 0.3s ease-out;
}

.toast-notification-container[class*="bottom"] .toast-notification {
  animation: slideInFromBottom 0.3s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Stack spacing */
.toast-notification-container .toast-notification + .toast-notification {
  margin-top: 0.5rem;
}

/* Progress indicator for timed toasts */
.toast-notification.timed::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background-color: currentColor;
  opacity: 0.3;
  animation: progress linear;
}

@keyframes progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}
