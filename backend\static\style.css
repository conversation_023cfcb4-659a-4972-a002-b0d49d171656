:root {
  --bg-color: #ffffff;
  --text-color: #333333;
  --primary-color: #4a90e2;
  --secondary-color: #f5f5f5;
  --border-color: #cccccc;
  --header-color: #333333;
  --button-bg: #4a90e2;
  --button-text: white;
  --modal-bg: #ffffff;
  --input-bg: #ffffff;
}

[data-theme="dark"] {
  --bg-color: #222222;
  --text-color: #f5f5f5;
  --primary-color: #4a90e2;
  --secondary-color: #333333;
  --border-color: #444444;
  --header-color: #f5f5f5;
  --button-bg: #4a90e2;
  --button-text: white;
  --modal-bg: #333333;
  --input-bg: #444444;
}

[data-color-scheme="blue"] {
  --primary-color: #4a90e2;
  --button-bg: #4a90e2;
}

[data-color-scheme="green"] {
  --primary-color: #4caf50;
  --button-bg: #4caf50;
}

[data-color-scheme="purple"] {
  --primary-color: #9c27b0;
  --button-bg: #9c27b0;
}

body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 20px;
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color 0.3s, color 0.3s;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

h1, h2, h3 {
  color: var(--header-color);
}

#form {
  margin-bottom: 20px;
  background-color: var(--secondary-color);
  padding: 15px;
  border-radius: 5px;
}

input, button, select {
  padding: 8px;
  margin: 5px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--input-bg);
  color: var(--text-color);
}

button {
  background-color: var(--button-bg);
  color: var(--button-text);
  cursor: pointer;
  border: none;
}

button:hover {
  opacity: 0.9;
}

.icon-button {
  font-size: 1.5rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
}

.header-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.button {
  background-color: var(--button-bg);
  color: var(--button-text);
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
  font-size: 0.9rem;
}

.primary-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  font-size: 1rem;
  width: 100%;
  margin-top: 15px;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  background-color: var(--secondary-color);
  border-radius: 5px;
  overflow: hidden;
}

th, td {
  border: 1px solid var(--border-color);
  padding: 10px;
  text-align: left;
}

th {
  background-color: var(--secondary-color);
  color: var(--text-color);
  font-weight: 600;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: var(--modal-bg);
  margin: 10% auto;
  padding: 20px;
  border-radius: 5px;
  width: 80%;
  max-width: 500px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.close {
  color: var(--text-color);
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.settings-section {
  margin-bottom: 20px;
}

.theme-options {
  display: flex;
  gap: 15px;
}

.preference-item {
  margin-top: 10px;
}

#save_settings {
  display: block;
  width: 100%;
  padding: 10px;
  margin-top: 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* Login Form Styles */
.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--input-bg);
  color: var(--text-color);
}

.error-message {
  color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.1);
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  display: none;
}

.success-message {
  color: #2ecc71;
  background-color: rgba(46, 204, 113, 0.1);
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  display: none;
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
}

.form-footer {
  margin-top: 20px;
  text-align: center;
  font-size: 0.9rem;
}

.form-footer a {
  color: var(--primary-color);
  text-decoration: none;
}

.form-footer a:hover {
  text-decoration: underline;
}

/* User Info Panel */
.user-info {
  background-color: var(--secondary-color);
  padding: 10px 15px;
  border-radius: 4px;
  margin: 10px 0;
  border-left: 4px solid var(--primary-color);
}

.user-info p {
  margin: 5px 0;
}

#user_role {
  font-style: italic;
  color: var(--primary-color);
}

.user-info a {
  color: var(--primary-color);
  text-decoration: none;
}

.user-info a:hover {
  text-decoration: underline;
}

/* Tabs */
.tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 20px;
}

.tab-button {
  background: none;
  border: none;
  padding: 10px 15px;
  cursor: pointer;
  font-size: 1rem;
  color: var(--text-color);
  border-bottom: 2px solid transparent;
  margin-right: 5px;
}

.tab-button.active {
  border-bottom: 2px solid var(--primary-color);
  color: var(--primary-color);
  font-weight: bold;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Activity Log */
.activity-container {
  max-height: 300px;
  overflow-y: auto;
}

.activity-list {
  margin-top: 10px;
}

.activity-item {
  padding: 10px;
  border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-time {
  font-size: 0.8rem;
  color: #777;
  margin-top: 5px;
}

.loading-text {
  text-align: center;
  color: #777;
  font-style: italic;
}

/* Tool Management Styles */
.tools-container {
  max-height: 500px;
  overflow-y: auto;
}

.tools-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.search-box {
  margin-bottom: 15px;
}

.search-box input {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
}

.small-button {
  background-color: var(--button-bg);
  color: var(--button-text);
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 0.8rem;
  cursor: pointer;
}

#tools_table {
  width: 100%;
  border-collapse: collapse;
}

#tools_table th, #tools_table td {
  padding: 8px;
  text-align: left;
  border: 1px solid var(--border-color);
}

#tools_table th {
  background-color: var(--secondary-color);
  color: var(--text-color);
  font-weight: 600;
}

#tools_table tr:nth-child(even) {
  background-color: var(--secondary-color);
}

.tool-status {
  display: inline-block;
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 0.8rem;
  font-weight: bold;
}

.status-available {
  background-color: #2ecc71;
  color: white;
}

.status-checked-out {
  background-color: #e74c3c;
  color: white;
}