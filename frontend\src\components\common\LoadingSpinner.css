/**
 * LoadingSpinner Component Styles
 */

/* Base container styles */
.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Centered loading (default) */
.loading-centered {
  padding: 2rem;
  min-height: 200px;
}

/* Inline loading */
.loading-inline {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  min-height: auto;
}

/* Full screen loading */
.loading-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(2px);
}

/* Overlay loading */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

/* Minimal loading (no padding/spacing) */
.loading-minimal {
  padding: 0;
  min-height: auto;
}

/* Loading backdrop */
.loading-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(1px);
}

/* Loading content */
.loading-content {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.loading-inline .loading-content {
  flex-direction: row;
  gap: 0.5rem;
}

/* Spinner sizes */
.loading-spinner-xs {
  width: 1rem;
  height: 1rem;
}

.loading-spinner-sm {
  width: 1.25rem;
  height: 1.25rem;
}

.loading-spinner-md {
  width: 2rem;
  height: 2rem;
}

.loading-spinner-lg {
  width: 3rem;
  height: 3rem;
}

.loading-spinner-xl {
  width: 4rem;
  height: 4rem;
}

/* Loading text styles */
.loading-text {
  color: #6c757d;
  font-weight: 500;
  text-align: center;
  margin: 0;
}

.loading-text-xs {
  font-size: 0.75rem;
}

.loading-text-sm {
  font-size: 0.875rem;
}

.loading-text-md {
  font-size: 1rem;
}

.loading-text-lg {
  font-size: 1.125rem;
}

.loading-text-xl {
  font-size: 1.25rem;
}

/* Inline text positioning */
.loading-inline .loading-text {
  margin: 0;
}

/* Progress bar styles */
.loading-progress {
  width: 200px;
  margin-top: 0.5rem;
}

.loading-progress-bar {
  height: 8px;
  border-radius: 4px;
}

/* Animation enhancements */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pulse animation for overlay */
.loading-overlay .loading-content {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .loading-centered {
    padding: 1rem;
    min-height: 150px;
  }
  
  .loading-text-lg {
    font-size: 1rem;
  }
  
  .loading-text-xl {
    font-size: 1.125rem;
  }
  
  .loading-progress {
    width: 150px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .loading-fullscreen {
    background-color: rgba(33, 37, 41, 0.9);
  }
  
  .loading-backdrop {
    background-color: rgba(33, 37, 41, 0.8);
  }
  
  .loading-text {
    color: #adb5bd;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .loading-text {
    color: #000;
    font-weight: 600;
  }
  
  .loading-backdrop {
    background-color: rgba(255, 255, 255, 0.95);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
  }
  
  .loading-overlay .loading-content {
    animation: none;
  }
}

/* Focus styles for accessibility */
.loading-spinner-container:focus {
  outline: 2px solid #0d6efd;
  outline-offset: 2px;
}

/* Loading states for specific operations */
.loading-operation-critical {
  border: 2px solid #dc3545;
  border-radius: 8px;
  padding: 1rem;
  background-color: #fff5f5;
}

.loading-operation-warning {
  border: 2px solid #ffc107;
  border-radius: 8px;
  padding: 1rem;
  background-color: #fffbf0;
}

.loading-operation-success {
  border: 2px solid #198754;
  border-radius: 8px;
  padding: 1rem;
  background-color: #f0fff4;
}

/* Button loading states */
.btn .loading-spinner {
  margin-right: 0.5rem;
}

.btn .loading-inline {
  padding: 0;
  min-height: auto;
}

/* Table loading overlay */
.table-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Card loading overlay */
.card-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
}
