:root {
  --bs-primary: #0d6efd;
  --bs-secondary: #6c757d;
  --bs-success: #198754;
  --bs-info: #0dcaf0;
  --bs-warning: #ffc107;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom cursor for sortable columns */
.cursor-pointer {
  cursor: pointer;
}

/* Card styling */
.card {
  border-radius: 0.5rem;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
  border-top-left-radius: 0.5rem !important;
  border-top-right-radius: 0.5rem !important;
}

/* Button styling */
.btn {
  border-radius: 0.25rem;
}

/* Table styling */
.table th {
  font-weight: 600;
  border-bottom-width: 2px;
}

/* Table header styling */
.table thead, .table thead.bg-light, thead.bg-light {
  background-color: var(--bs-light);
}

.table th {
  color: var(--bs-body-color);
}

/* Dark mode table styling */
[data-bs-theme="dark"] .table thead.bg-light,
[data-bs-theme="dark"] thead.bg-light {
  background-color: var(--bs-dark);
  color: var(--bs-body-color);
}

[data-bs-theme="dark"] .table th {
  border-color: #495057;
  color: var(--bs-body-color);
}

[data-bs-theme="dark"] .table td {
  border-color: #495057;
}

/* Navbar styling */
.navbar-brand {
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* Full width container */
.container-fluid {
  max-width: 100%;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* Dashboard cards */
.display-4 {
  font-size: 3rem;
  font-weight: 500;
}

/* Shadow effects */
.shadow-sm {
  box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
}

/* Transitions */
.btn, .card, .nav-link {
  transition: all 0.2s ease-in-out;
}

.btn:hover {
  transform: translateY(-1px);
}

/* Avatar circles */
.avatar-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 500;
  margin: 0 auto;
}

.avatar-circle-sm {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Profile page styles */
.profile-avatar {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--bs-primary);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.profile-avatar-placeholder {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
  font-weight: 500;
  background-color: var(--bs-primary);
  color: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Dark mode styles */
[data-bs-theme="dark"] {
  --bs-body-bg: #212529;
  --bs-body-color: #f8f9fa;
  --bs-primary: #3d8bfd;
  --bs-secondary: #6c757d;
  --bs-success: #42ba96;
  --bs-info: #56cdfc;
  --bs-warning: #ffca2c;
  --bs-danger: #ff4d6b;
  --bs-light: #343a40;
  --bs-dark: #1a1d20;
  --bs-border-color: #495057;
}

/* Fix for card headers with bg-light class in dark mode */
[data-bs-theme="dark"] .card-header.bg-light {
  background-color: var(--bs-dark) !important;
  color: var(--bs-body-color);
}

[data-bs-theme="light"] {
  --bs-body-bg: #f8f9fa;
  --bs-body-color: #212529;
  --bs-primary: #0d6efd;
  --bs-secondary: #6c757d;
  --bs-success: #198754;
  --bs-info: #0dcaf0;
  --bs-warning: #ffc107;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  --bs-border-color: #dee2e6;
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
  text-align: center;
}

.status-available {
  background-color: var(--bs-success);
  color: white;
}

.status-checked-out {
  background-color: var(--bs-warning);
  color: #212529;
}

.status-maintenance {
  background-color: var(--bs-info);
  color: white;
}

.status-retired {
  background-color: var(--bs-danger);
  color: white;
}

.status-active {
  background-color: var(--bs-success);
  color: white;
}

.status-inactive {
  background-color: var(--bs-secondary);
  color: white;
}

/* Spin animation for refresh icons */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spin {
  animation: spin 1s linear infinite;
}
