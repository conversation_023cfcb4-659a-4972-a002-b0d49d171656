# Backend Environment Variables
FLASK_ENV=production
SECRET_KEY=your-secure-secret-key
CORS_ORIGINS=http://localhost,http://localhost:80
PYTHONDONTWRITEBYTECODE=1
PYTHONUNBUFFERED=1

# Frontend Environment Variables
VITE_API_URL=http://localhost:5000

# Docker Compose Resource Limits (optional)
# CPU limits use fractional values (e.g., 0.5 = 50% of one CPU core)
# Memory limits use suffixes: M for megabytes, G for gigabytes
BACKEND_CPU_LIMIT=0.5
BACKEND_MEMORY_LIMIT=512M
FRONTEND_CPU_LIMIT=0.3
FRONTEND_MEMORY_LIMIT=256M
