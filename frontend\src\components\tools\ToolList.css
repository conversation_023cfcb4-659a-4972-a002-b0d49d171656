.filter-section {
  background-color: #f8f9fa;
  border-radius: 0.25rem;
  padding: 1rem;
  margin-top: 1rem;
  border: 1px solid #dee2e6;
}

.filter-badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
  margin-left: 0.5rem;
  vertical-align: middle;
}

.filter-button {
  display: flex;
  align-items: center;
}

.filter-button i {
  margin-right: 0.25rem;
}

/* Status badge styles */
.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-available {
  background-color: #d1e7dd;
  color: #0f5132;
}

.status-checked-out {
  background-color: #f8d7da;
  color: #842029;
}

.status-maintenance {
  background-color: #fff3cd;
  color: #664d03;
}

.status-retired {
  background-color: #e2e3e5;
  color: #41464b;
}

/* Calibration Status Indicators */
.calibration-status-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
}

.calibration-status-badge.sm {
  font-size: 0.7rem;
  padding: 0.2rem 0.4rem;
}

.calibration-status-badge.lg {
  font-size: 0.85rem;
  padding: 0.35rem 0.6rem;
}

.calibration-status-badge.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

@media (prefers-reduced-motion: reduce) {
  .calibration-status-badge.pulse {
    animation: none;
  }
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-pointer:hover {
  background-color: var(--bs-gray-100);
}
