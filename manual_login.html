<!DOCTYPE html>
<html>
<head>
    <title>Manual Login</title>
</head>
<body>
    <h1>Manual Login</h1>
    <button onclick="performLogin()">Login as ADMIN001</button>
    <div id="status"></div>

    <script>
        function performLogin() {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = 'Setting authentication tokens...';

            // Set mock tokens (these would normally come from the API)
            const mockUser = {
                id: 1,
                employee_number: 'ADMIN001',
                name: 'System Administrator',
                is_admin: true,
                department: 'IT',
                is_active: true,
                permissions: [],
                roles: []
            };

            const mockAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX25hbWUiOiJTeXN0ZW0gQWRtaW5pc3RyYXRvciIsImVtcGxveWVlX251bWJlciI6IkFETUlOMDAxIiwiaXNfYWRtaW4iOnRydWUsImRlcGFydG1lbnQiOiJJVCIsInBlcm1pc3Npb25zIjpbXSwiaWF0IjoxNzUwNDc1NzEyLCJleHAiOjE3NTA0NzY2MTIsInR5cGUiOiJhY2Nlc3MifQ.pLRQIPuj98ht5Y1gUxPtTnJpuxrr7R7AgSWjilQ1p6KY';
            const mockRefreshToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJpYXQiOjE3NTA0NzU3MTIsImV4cCI6MTc1MTA4MDUxMiwidHlwZSI6InJlZnJlc2giLCJqdGkiOiJjMzhhMWQ1MWJiN2QzOGQzZDY4ZjBjZThmZTFhYjZiOGYifQ.bPqH9q6GscJt5pWHxzI9ze87sDbm9W8txjbAqtsHMcM';

            // Store tokens in localStorage
            localStorage.setItem('supplyline_access_token', mockAccessToken);
            localStorage.setItem('supplyline_refresh_token', mockRefreshToken);
            localStorage.setItem('supplyline_user_data', JSON.stringify(mockUser));

            statusDiv.innerHTML = 'Authentication tokens set! Redirecting to dashboard...';

            // Redirect to dashboard
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 1000);
        }
    </script>
</body>
</html>
