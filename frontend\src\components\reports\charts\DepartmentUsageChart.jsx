import { Card } from 'react-bootstrap';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Toolt<PERSON>, Legend, ResponsiveContainer } from 'recharts';

// Colors for the pie chart
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82ca9d', '#ffc658'];

const DepartmentUsageChart = ({ data }) => {
  // Check if we have the necessary data
  if (!data || !data.checkoutsByDepartment || data.checkoutsByDepartment.length === 0) {
    return (
      <Card className="shadow-sm">
        <Card.Header className="bg-light">
          <h5 className="mb-0">Department Usage Distribution</h5>
        </Card.Header>
        <Card.Body className="text-center text-muted p-5">
          No department usage data available
        </Card.Body>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <Card.Header className="bg-light">
        <h5 className="mb-0">Department Usage Distribution</h5>
      </Card.Header>
      <Card.Body>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={data.checkoutsByDepartment}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={100}
              fill="#8884d8"
              dataKey="value"
              nameKey="name"
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            >
              {data.checkoutsByDepartment.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip formatter={(value, name) => [`${value} checkouts`, name]} />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </Card.Body>
    </Card>
  );
};

export default DepartmentUsageChart;
