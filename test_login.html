<!DOCTYPE html>
<html>
<head>
    <title>Test Login</title>
</head>
<body>
    <h1>Test Login Functionality</h1>
    <button onclick="testLogin()">Test Login API</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing login...';

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        employee_number: 'ADMIN001',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <h2>Login Successful!</h2>
                        <p>Access Token: ${data.access_token.substring(0, 50)}...</p>
                        <p>User: ${data.user.name}</p>
                        <p>Employee Number: ${data.user.employee_number}</p>
                        <p>Is Admin: ${data.user.is_admin}</p>
                        <p>Department: ${data.user.department}</p>
                    `;
                    
                    // Store tokens
                    localStorage.setItem('supplyline_access_token', data.access_token);
                    localStorage.setItem('supplyline_refresh_token', data.refresh_token);
                    localStorage.setItem('supplyline_user_data', JSON.stringify(data.user));
                    
                    // Redirect to dashboard after 2 seconds
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 2000);
                } else {
                    resultDiv.innerHTML = `<h2>Login Failed</h2><p>${JSON.stringify(data)}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<h2>Error</h2><p>${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
